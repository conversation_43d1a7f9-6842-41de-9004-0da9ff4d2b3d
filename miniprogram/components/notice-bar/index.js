const formatKey = (e) =>`notice_bar_${e}` 

Component({
  properties: {
    // 基础配置
    text: String,
    closable: {
      type: Boolean,
      value: true,
    },
    key: {
      type: String,
      value: 'default_notice_bar',
    },

    // 图标配置
    icon: String,
    iconSize: {
      type: Number,
      value: 40,
    },

    // 滚动配置
    scrollable: Boolean,
    scrollSpeed: {
      type: Number,
      value: 10,
    },
  },

  data: {
    visible: true,
  },

  lifetimes: {
    attached() {
      const storageValue = wx.getStorageSync(formatKey(this.properties.key));
      if (storageValue) this.setData({ visible: false });
    },
  },

  methods: {
    // 关闭处理
    handleClose() {
      this.setData({ visible: false });
      wx.setStorageSync(formatKey(this.properties.key), true);
      this.triggerEvent('close');
    },

    // 图标点击
    onIconClick() {
      this.triggerEvent('iconclick');
    },

    // 重置状态
    reset() {
      wx.removeStorageSync(formatKey(this.properties.key));
      this.setData({ visible: true });
    },
  },
});
