.notice-bar {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: rgba(255, 229, 143, 0.3);
  border-radius: 16rpx;
  position: relative;
}

.icon-container {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.icon-container image {
  border-radius: 8rpx;
}

.content-container {
  flex: 1;
  overflow: hidden;
}

.content {
  color: #ef7c00;
  font-size: 28rpx;
  line-height: 1.4;
  white-space: nowrap;
  display: inline-block;
  padding-right: 40rpx;
}

.content.scroll {
  animation: marquee linear infinite;
}

@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.close-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  padding: 12rpx;
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
}
