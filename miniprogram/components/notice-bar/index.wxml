<!-- components/notice-bar/notice-bar.wxml -->
<view wx:if="{{ visible }}" class="notice-bar">
  <!-- 左侧图标 -->
  <view wx:if="{{ icon }}" class="icon-container" bindtap="onIconClick">
    <image
      src="{{ icon }}"
      style="width: {{ iconSize }}rpx; height: {{ iconSize }}rpx;"
      mode="aspectFill"
    />
  </view>

  <!-- 通知内容 -->
  <view class="content-container {{ icon ? 'has-icon' : '' }}">
    <text
      class="content {{ scrollable ? 'scroll' : '' }}"
      style="animation-duration: {{ scrollSpeed }}s;"
      >{{ text }}</text
    >
  </view>

  <!-- 关闭按钮 -->
  <view wx:if="{{ closable }}" class="close-btn" bindtap="handleClose">
    <image src="/assets/image/icon/close.svg" class="close-icon" mode="aspectFill" />
  </view>
</view>
