import { getImageUrl } from '@/utils/format';

Component({
  properties: {
    src: { type: String, value: '' }, // 原始路径
    mode: { type: String, value: 'aspectFill' }, // 图片展示模式
    defaultImg: { type: String, value: './defalut-image.svg' }, // 默认图
    width: { type: String, value: '100%' }, // 宽度
    height: { type: String, value: '100%' }, // 高度
    radius: { type: String, value: '0' }, // 圆角
    preview: { type: Boolean, value: false }, // 是否点击预览
  },
  data: {
    realSrc: '',
    oldSrc: '',
    loading: false,
  },
  observers: {
    src: function (newVal) {
      const oldSrc = this.data.oldSrc;
      if (newVal !== oldSrc) {
        this.setData({ loading: true }); // 每次换图重新 loading
        this.processSrc();
      }
    },
  },
  methods: {
    processSrc() {
      const { src, defaultImg } = this.properties;
      const realSrc = getImageUrl(src, defaultImg);
      this.setData({ realSrc, oldSrc: src }, () => {
        this.handleLoad();
      });
    },
    handleLoad() {
      this.setData({ loading: false, oldSrc: this.properties.src });
    },
    handleError() {
      this.setData({ realSrc: this.properties.defaultImg, loading: false });
    },
    handlePreview() {
      if (this.properties.preview) {
        wx.previewImage({
          urls: [this.data.realSrc],
        });
      }
    },
  },
});
