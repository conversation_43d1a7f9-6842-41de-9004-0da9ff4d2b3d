.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

.dialog-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s ease;
}

.dialog-content {
  position: relative;
  width: 640rpx;
  background-color: #fff;
  border-radius: 16rpx;
  // padding: 32rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all 0.3s ease;
}

.dialog-enter {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.dialog-leave {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

.dialog-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 24rpx;
}

.dialog-body {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.dialog-actions {
  display: flex;
  justify-content: space-between;
}

.dialog-actions.vertical {
  flex-direction: column;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.confirm-btn {
  background-color: #07c160;
  color: #fff;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
  margin-right: 20rpx;
}

.dialog-actions.vertical .cancel-btn {
  margin-right: 0;
  margin-bottom: 20rpx;
}
