// components/dialog/index.js
Component({
  behaviors: [],
  properties: {
    visible: {
      type: Boolean,
      value: false,
    },
    title: String,
    content: String,
    confirmText: {
      type: String,
      value: '确认',
    },
    cancelText: {
      type: String,
      value: '取消',
    },
    showFooter: {
      type: Boolean,
      value: false,
    },
    showCancel: {
      type: Boolean,
      value: false,
    },
    maskClosable: {
      type: Boolean,
      value: false,
    },
    buttonLayout: {
      type: String,
      value: 'horizontal', // 或 vertical
    },
  },

  data: {
    maskOpacity: 0,
    animationClass: '',
  },

  observers: {
    visible: function (visible) {
      if (visible) {
        this.showAnimation();
      } else {
        this.hideAnimation();
      }
    },
  },

  methods: {
    // 显示动画
    showAnimation() {
      wx.nextTick(() => {
        this.setData({
          maskOpacity: 1,
          animationClass: 'dialog-enter',
        });
      });
    },

    // 隐藏动画
    hideAnimation() {
      this.setData({
        maskOpacity: 0,
        animationClass: 'dialog-leave',
      });
    },

    onConfirm() {
      this.triggerEvent('confirm');
      this.close();
    },

    onCancel() {
      this.triggerEvent('cancel');
      this.close();
    },

    onClickMask() {
      if (this.data.maskClosable) {
        this.close();
      }
    },
    close() {
      this.setData({ visible: false });
      this.triggerEvent('close');
    },
  },
});
