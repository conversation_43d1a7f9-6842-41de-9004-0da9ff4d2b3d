<view class="dialog-container" wx:if="{{ visible }}">
  <!-- 遮罩层 -->
  <view class="dialog-mask" catch:tap="onClickMask" style="opacity: {{ maskOpacity }}"></view>

  <!-- 对话框主体 -->
  <view class="dialog-content {{ animationClass }}">
    <!-- 标题 -->
    <view class="dialog-title" wx:if="{{ title }}">{{ title }}</view>

    <!-- 内容区域 -->
    <view class="dialog-body">
      <slot name="content" wx:if="{{ !content }}"></slot>
      <text wx:else>{{ content }}</text>
    </view>

    <!-- 操作按钮 -->
    <view wx:if="{{ showFooter }}" class="dialog-actions {{ buttonLayout }}">
      <view class="btn cancel-btn" wx:if="{{ showCancel }}" bind:tap="onCancel">
        {{ cancelText }}
      </view>
      <view class="btn confirm-btn" bind:tap="onConfirm">{{ confirmText }}</view>
    </view>
  </view>
</view>
