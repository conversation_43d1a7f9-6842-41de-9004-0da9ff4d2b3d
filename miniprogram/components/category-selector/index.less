.popup-block {
  padding: 0 0 12rpx;
  // --td-tree-item-active-color: red;
  //   --td-tree-bg-color	@bg-color-container	-
  // --td-tree-colum-width	206rpx	-
  // --td-tree-item-active-color	@brand-color	-
  // --td-tree-item-font-size	32rpx	-
  // --td-tree-item-height	112rpx	-
  // --td-tree-root-bg-color	@bg-color-secondarycontainer
  // t-class	根节点样式类
  // t-class-left-column	左侧第一列样式类
  // t-class-left-item	左侧第一列子项样式类
  // t-class-middle-item	中间列子项样式类
  // t-class-right-column	右侧第一列样式类
  // t-class-right-item	右侧第一列子项样式类
  // t-class-right-item-label	右侧第一列子项标签样式类
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-close {
      padding: 36rpx 32rpx;
      .ic {
        width: 24rpx;
        height: 24rpx;
      }
    }
    .title {
      height: 32rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #333;
      line-height: 32rpx;
      text-align: center;
      padding: 32rpx 0 48rpx;
    }
    .btn-confirm {
      height: 32rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: @brand-color;
      line-height: 32rpx;
      padding: 32rpx 32rpx 48rpx;
    }
  }

  .current-cate {
    height: 40rpx;
    font-size: 28rpx;
    color: #333;
    line-height: 40rpx;
    padding: 0 48rpx;
    margin-bottom: 24rpx;
  }
  .content {
    .tree-select {
      overflow: hidden;
    }
    .empty-block {
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: #999;
    }
  }
}
