import { businessCategoryListAll } from '@/api/index';

const app = getApp();
/**
 * 拍平嵌套对象，并生成一个以 id 为键、name 为值的映射表
 * @param {Object} obj - 嵌套对象
 * @param {Object} result - 存储拍平后的结果
 * @returns {Object} - 拍平后的对象
 */
const flattenObject = (obj, result = {}) => {
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      // 如果当前值是对象，并且包含 label 和 value
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        if (obj[key].label && obj[key].value) {
          result[obj[key].value] = obj[key].label; // 将 label 和 value 存入结果
        }
        flattenObject(obj[key], result); // 递归处理嵌套对象
      }
    }
  }
  return result;
};

const getLabelByValue = (val, flatMap) => flatMap[val] || '';

const generateTree = (data) => {
  // 外部数组，用于保存每一层级的第一个值的 id 和 name
  const firstItems = [];
  // 递归函数，用于生成树形结构
  const buildTree = (items, level = 0) => {
    return items
      .sort((a, b) => a.sortId - b.sortId) // 按 sortId 排序
      .map((item, index) => {
        // 如果是该层级的第一个元素，则保存 id 和 name
        if (index === 0) {
          if (!firstItems[level]) {
            firstItems[level] = { id: item.id, name: item.name };
          }
        }
        return {
          label: item.name, // 节点名称
          value: item.id, // 节点值
          children: item.subCategory ? buildTree(item.subCategory, level + 1) : null, // 递归生成子节点
        };
      }
        // ({
        //   label: item.name, // 节点名称
        //   value: item.id, // 节点值
        //   children: item.subCategory ? buildTree(item.subCategory) : null, // 递归生成子节点
        // }));
      )
  };

  // 调用递归函数，从根节点开始生成树
  const tree = buildTree(data);

  // 返回生成的树形结构以及每层的第一个值的 id 和 name 数组
  return { tree, firstItems };
};

Component({
  options: {
    styleIsolation: 'shared',
  },
  properties: {
    hasTabbar: {
      type: Boolean,
      value: false,
    },
    cityId: {
      type: String,
      value: '',
    },
  },
  data: {
    customTabbarHeight: app.globalData.customTabbarHeight,
    show: false,
    options: [],
    value: [],
    currentCateText: '',
    loading: false,
  },
  methods: {
    openIt() {
      const { cityId } = this.data;

      if (!cityId) {
        wx.showToast({ title: '请先选择城市', icon: 'none' });
        return;
      }

      this.setData({
        show: true,
        loading: true,
        value: '',
        currentCateText: '',
        options: [],
      });
      this.getData(cityId);
    },
    closeIt() {
      this.setData({ show: false });
    },
    onCancel() {
      this.closeIt();
      this.triggerEvent('cancel');
    },
    async getData(cityId) {

      if (this.categoryCityId !== cityId || !this.categoryList) {
        const res = await businessCategoryListAll({ cityId });


        if (res.success) {
          const list = res.data ?? [];
          if (list.length > 0) {
            const { tree, firstItems } = generateTree(list)
            this.categoryList = tree;
            this.categoryCityId = cityId;
            this.firstItems = firstItems
          }
        }
      }

      if (this.categoryCityId !== cityId) {
        this.categoryList = null;
        this.categoryCityId = null;
      }
      const value = this.firstItems ? this.firstItems.map(e => e.id) : []
      const valueText = this.firstItems ? this.firstItems.map(e => e.name) : []
      this.setData({
        loading: false,
        options: this.categoryList || [],
        value,
        valueText,
        currentCateText: valueText.join(' > '),
      });
      // 拍平数据，生成映射表
      this.flatCategoryMap = flattenObject(this.categoryList);
    },
    onTreeChange(e) {
      const { value } = e.detail;
      const text1 = getLabelByValue(value[0], this.flatCategoryMap);
      const text2 = getLabelByValue(value[1], this.flatCategoryMap);
      const text3 = getLabelByValue(value[2], this.flatCategoryMap);
      const valueText = [text1, text2, text3];
      console.log({
        value,
        valueText,
        currentCateText: valueText.join(' > '),
      });

      this.setData({
        value,
        valueText,
        currentCateText: valueText.join(' > '),
      });
    },
    onVisibleChange(e) {
      this.setData({ show: e.detail.visible });
    },
    onConfirm() {
      const { options, value, valueText, showCateText } = this.data;
      if (options.length === 0) {
        this.closeIt();
        return;
      }

      if (value.length === 0) {
        wx.showToast({ title: '请选择分类', icon: 'none' });
        return;
      }

      const data = {
        value,
        largeCategory: value[0], // 经营品类-大类
        mediumCategory: value[1], // 经营品类-中类
        smallCategory: value[2], // 经营品类-小类
        largeCategoryName: valueText[0],
        mediumCategoryName: valueText[1],
        smallCategoryName: valueText[2],
        showCateText,
      };

      this.triggerEvent('confirm', data);
      this.closeIt();
    },
  },
});
