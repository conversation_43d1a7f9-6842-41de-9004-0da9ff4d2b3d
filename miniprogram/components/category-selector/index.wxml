<t-popup
  visible="{{ show }}"
  usingCustomNavbar
  placement="bottom"
  closeOnOverlayClick
  close-on-overlay-click
  bind:visible-change="onVisibleChange"
>
  <view class="popup-block">
    <view class="header">
      <view class="btn-close" bindtap="onCancel">
        重置
        <!-- <image class="ic" src="/assets/image/icon/close.svg" /> -->
      </view>
      <view class="title">经营品类</view>
      <view class="btn-confirm" bindtap="onConfirm">确定</view>
    </view>

    <view class="content">
      <view class="current-cate">当前品类：{{ currentCateText }}</view>
      <!-- t-class-left-column="left-column"
      t-class-left-item="left-item" -->
      <view wx:if="{{ !loading && options.length === 0 }}" class="empty-block"> 暂无品类 </view>
      <t-tree-select
        t-class="tree-select"
        options="{{ options }}"
        value="{{ value }}"
        bind:change="onTreeChange"
      />
    </view>
  </view>
  <view wx:if="{{ hasTabbar }}" slot="footer" style="height: {{ customTabbarHeight }}px"></view>
</t-popup>
