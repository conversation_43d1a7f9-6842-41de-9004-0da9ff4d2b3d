<view class="load-more" wx:if="{{ show }}">
  <view class="content">
    <view wx:if="{{ !isEnd }}">
      <!-- loading 动画 -->
      <view class="loading" wx:if="{{ loading }}">
        <view class="loader">
          <view class="dot-loading">
            <view class="dot dot1"></view>
            <view class="dot dot2"></view>
            <view class="dot dot3"></view>
          </view>
        </view>
        <text class="text"> 加载中...</text>
      </view>
      <!-- 默认提示 -->
      <text class="tip-text" wx:else>上拉加载更多</text>
    </view>
    <!-- 全部加载完成提示 -->
    <text class="end-text" wx:else>已经到底了</text>
  </view>
</view>
