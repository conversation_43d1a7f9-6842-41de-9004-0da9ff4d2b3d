Component({
  options: { multipleSlots: !0 },
  properties: {
    title: { type: String, value: '' },
    showCancel: { type: Boolean, value: 0 },
    cancelText: { type: String, value: '取消' },
    maskClass: { type: String, value: '' },
    extClass: { type: String, value: '' },
    maskClosable: { type: <PERSON>olean, value: !0 },
    mask: { type: Boolean, value: !0 },
    show: { type: Boolean, value: !1, observer: '_showChange' },
    actions: { type: Array, value: [], observer: '_groupChange' },
    rootPortal: { type: Boolean, value: !1 },
  },
  data: { wrapperShow: !1, innerShow: !1 },
  lifetimes: {
    ready: function () {
      this._showChange(this.data.show);
    },
  },
  methods: {
    _showChange: function (e) {
      var t = this;
      e
        ? this.setData({ wrapperShow: !0, innerShow: !0 })
        : (this.setData({ innerShow: !1 }),
          setTimeout(function () {
            t.setData({ wrapperShow: !1 });
          }, 10));
    },
    _groupChange: function (e) {
      e.length > 0 &&
        'string' != typeof e[0] &&
        !(e[0] instanceof Array) &&
        this.setData({ actions: [this.data.actions] });
    },
    buttonTap: function (e) {
      var t = e.currentTarget.dataset,
        a = t.value,
        o = t.groupindex,
        n = t.index;
      this.triggerEvent('actiontap', { value: a, groupindex: o, index: n });
    },
    closeActionSheet: function (e) {
      var t = e.currentTarget.dataset.type;
      (this.data.maskClosable || t) && (this.setData({ show: !1 }), this.triggerEvent('close'));
    },
  },
});
