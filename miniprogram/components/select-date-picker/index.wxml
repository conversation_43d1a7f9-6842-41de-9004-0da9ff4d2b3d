<view class="container" bindtap="onTap">
  <view class="text">{{ value || '选择日期' }}</view>
  <t-icon name="calendar" size="30rpx" color="#999" />
</view>
<t-date-time-picker
  title="选择日期"
  visible="{{ visible }}"
  mode="date"
  value="{{ value }}"
  format="YYYY-MM-DD"
  bindchange="onConfirm"
  bindcancel="hidePicker"
  start="{{ start }}"
  end="{{ end }}"
>
  <view wx:if="{{ hasTabbar }}" slot="footer" style="height: {{ customTabbarHeight }}px"></view>
</t-date-time-picker>

<!-- bindpick="onColumnChange"
  bindcancel="hidePicker" -->
