const today = new Date();

// 格式化日期函数 yyyy-MM-dd
const formatDate = (date) => {
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, '0');
  const d = String(date.getDate()).padStart(2, '0');
  return `${y}-${m}-${d}`;
};

// 今日
const todayDate = formatDate(today);
const app = getApp();

Component({
  properties: {
    hasTabbar: {
      type: Boolean,
      value: false,
    },
    value: {
      type: String,
      value: '',
    },
  },
  data: {
    customTabbarHeight: app.globalData.customTabbarHeight,
    todayDate,
    start: '2025-01-01',
    end: todayDate,
    visible: false,
  },
  methods: {
    hidePicker() {
      this.setData({ visible: false });
    },
    onTap() {
      this.setData({ visible: true });
    },
    onConfirm(e) {
      this.setData({
        // value: e.detail.value,
        visible: false,
      });
      // this.triggerEvent('actiontap', { value: a, groupindex: o, index: n });
      this.triggerEvent('change', { value: e.detail.value });
    },
  },
});
