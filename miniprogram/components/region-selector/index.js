import { allAddressList } from '@/api/index';

const app = getApp();

function getAllAddress(list) {
  const provinces = {};
  const cities = {};
  const counties = {};

  function traverse(data) {
    data.forEach((item) => {
      if (item.parentId === 100000) {
        // 添加省级信息
        provinces[item.id] = item.name;
      }
      item.subAddress?.forEach((subItem) => {
        if (subItem.parentId === item.id) {
          // 添加市级信息
          cities[subItem.id] = subItem.name;
        }
        subItem.subAddress?.forEach((countyItem) => {
          // 添加区县级信息
          counties[countyItem.id] = countyItem.name;
        });
      });
    });
  }

  if (list?.length > 0) {
    traverse(list);
  }

  return { provinces, cities, counties };
}

const getOptions = (obj, filter) => {
  const res = Object.keys(obj).map((key) => ({ value: key, label: obj[key] }));

  if (filter) {
    return res.filter(filter);
  }

  return res;
};

const match = (v1, v2, size) => v1.toString().slice(0, size) === v2.toString().slice(0, size);

Component({
  properties: {
    hasTabbar: {
      type: Boolean,
      value: false,
    },
    cancelBtn: {
      type: String,
      value: '取消',
    },
  },
  data: {
    customTabbarHeight: app.globalData.customTabbarHeight,
    show: false,
    provinces: [],
    cities: [],
    counties: [],
  },
  methods: {
    openIt() {
      this.setData({ show: true });
      this.getData();
    },
    closeIt() {
      this.setData({ show: false });
    },
    onCancel() {
      this.closeIt();
      this.triggerEvent('cancel');
    },
    async getData() {
      if (!this.areaList) {
        const res = await allAddressList();
        const list = res.data ?? [];
        const areaList = getAllAddress(list);

        this.areaList = areaList || [];
      }

      this.initData(this.areaList);
    },
    initData(data) {
      const newProvinces = getOptions(data.provinces) ?? [];
      const proviceValue = newProvinces[0]?.value;

      if (proviceValue) {
        const { cities, counties } = this.getCities(proviceValue);

        this.setData({ provinces: newProvinces, cities, counties });
      }
    },
    onColumnChange(e) {
      const { column, index } = e.detail;
      const { provinces, cities } = this.data;

      if (column === 0) {
        // 更改省份
        const { cities, counties } = this.getCities(provinces[index].value);

        this.setData({ cities, counties });
      }

      if (column === 1) {
        // 更改城市
        const counties = this.getCounties(cities[index].value);

        this.setData({ counties });
      }

      if (column === 2) {
        // 更改区县
      }
    },

    getCities(provinceValue) {
      const cities = getOptions(this.areaList.cities, (city) =>
        match(city.value, provinceValue, 2)
      );
      const counties = this.getCounties(cities[0].value);

      return { cities, counties };
    },

    getCounties(cityValue) {
      return getOptions(this.areaList.counties, (county) => match(county.value, cityValue, 4));
    },

    onPickerChange(e) {
      const { value, label } = e.detail;
      this.setData({
        areaVisible: false,
        areaValue: value,
        areaText: label.join(' '),
      });
    },
    onConfirm(e) {
      const { value, label } = e.detail;

      const data = {
        ...e.detail,
        provinceId: value[0],
        cityId: value[1],
        countyId: value[2],
        provinceName: label[0],
        cityName: label[1],
        districtName: label[2],
      };

      this.triggerEvent('confirm', data);
    },
  },
});
