<t-picker
  visible="{{ show }}"
  value="{{ value }}"
  title="选择地区"
  cancelBtn="{{ cancelBtn }}"
  confirmBtn="确认"
  usingCustomNavbar
  usePopup
  bindchange="onConfirm"
  bindpick="onColumnChange"
  bindcancel="onCancel"
>
  <!-- t-class-content="custom-action-sheet"
  append-to="body" -->
  <t-picker-item options="{{ provinces }}"></t-picker-item>
  <t-picker-item options="{{ cities }}"></t-picker-item>
  <t-picker-item options="{{ counties }}"></t-picker-item>
  <view wx:if="{{ hasTabbar }}" slot="footer" style="height: {{ customTabbarHeight }}px"></view>
</t-picker>
