<view class="tab-bar" height="{{ customTabbarHeight }}px">
  <view class="tab-bar-border"></view>
  <view
    wx:for="{{ list }}"
    wx:key="index"
    class="tab-bar-item {{ index === 2 ? 'tab-bar-add' : '' }}"
    data-path="{{ item.pagePath }}"
    data-index="{{ index }}"
    bindtap="switchTab"
  >
    <image src="{{ selected === index ? item.selectedIconPath : item.iconPath }}"></image>
    <view style="color: {{ selected === index ? selectedColor : color }}">{{ item.text }}</view>
  </view>
</view>
