const app = getApp();
const { customTabbarHeight } = app.globalData;

Component({
  data: {
    customTabbarHeight,
    selected: 0,
    color: '#666',
    selectedColor: '#54d601',
    backgroundColor: '#fff',
    list: [
      {
        pagePath: '/pages/home/<USER>',
        iconPath: '/assets/image/tabbar/home.png',
        selectedIconPath: '/assets/image/tabbar/home-active.png',
        text: '首页',
      },
      {
        pagePath: '/pages/activity/index',
        iconPath: '/assets/image/tabbar/activity.png',
        selectedIconPath: '/assets/image/tabbar/activity-active.png',
        text: '活动',
      },
      {
        pagePath: '/subpages/activity/add/index',
        iconPath: '/assets/image/tabbar/activity-add.png',
        selectedIconPath: '/assets/image/tabbar/activity-add.png',
        text: '',
      },
      {
        pagePath: '/pages/order/index',
        iconPath: '/assets/image/tabbar/order.png',
        selectedIconPath: '/assets/image/tabbar/order-active.png',
        text: '订单',
      },
      {
        pagePath: '/pages/mine/index',
        iconPath: '/assets/image/tabbar/mine.png',
        selectedIconPath: '/assets/image/tabbar/mine-active.png',
        text: '我的',
      },
    ],
  },
  attached() {},
  methods: {
    switchTab(e) {
      const { path: url, index: selected } = e.currentTarget.dataset;

      if (selected === 2) {
        // 添加活动
        wx.navigateTo({ url });
      } else {
        wx.switchTab({ url });
        this.setData({ selected });
      }
    },
  },
});
