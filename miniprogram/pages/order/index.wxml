<t-navbar title="订单" />
<view class="flex items-center justify-between filter-box">
  <select-date-picker value="{{ orderDate }}" hasTabbar onchange="onChangeDate" />
  <view class="flex items-center justify-center item" bindtap="handleActionOrderType">
    <text class="text">{{ enums.ConOrderStatus[status] || '--' }}</text>
    <t-icon class="ic" name="caret-down-small" size="30rpx" color="#999" />
  </view>
  <view class="flex items-center justify-center item" bindtap="onChangeSortType">
    <text class="text">{{ sortType === 1 ? '倒序' : '正序' }}</text>
    <t-icon class="ic" name="arrow-up-down-1" size="30rpx" color="#999" />
  </view>
</view>

<scroll-view
  scroll-y
  style="flex: 1"
  show-scrollbar="{{ false }}"
  enhanced
  refresher-enabled
  refresher-triggered="{{ refresherStatus }}"
  bindrefresherrefresh="onPullDownRefresh"
  bindscrolltolower="onReachBottom"
>
  <view class="page">
    <view class="order-list">
      <view wx:for="{{ list }}" wx:key="index" class="order-card">
        <view class="flex items-center justify-between title">
          <view class="name-text1">{{ item.shopName || '--' }}</view>
          <view class="name-text2 status-{{ item.status }}">{{ item.statusText || '--' }}</view>
        </view>
        <view class="flex items-center justify-between name">
          <view class="flex flex-col">
            <view class="flex items-center name-block">
              <view class="block1">{{ item.fullName || '--' }}</view>
              <view class="firstFlagText firstFlagText-{{ item.firstFlag }}">
                {{ item.firstFlagText || '--' }}
              </view>
            </view>
            <view class="block2">已累计消费{{ item.consumeCount || 0 }}次</view>
          </view>
          <image
            class="ic-phone"
            src="/assets/image/icon/phone3.png"
            data-item="{{ item }}"
            bindtap="onCallPhone"
          />
        </view>
        <view class="items-block">
          <view class="flex items-center justify-between item">
            <view class="label">实付金额({{ item.consumerCount || 0 }}人)</view>
            <view class="value">¥{{ item.consumerAmount || 0 }}</view>
          </view>
          <view class="flex items-center justify-between item">
            <view class="label">优惠</view>
            <view class="value">-¥{{ item.retailPrice || 0 }}</view>
          </view>
          <view class="flex items-center justify-between item">
            <view class="label label2">实付</view>
            <view class="value value2">¥{{ item.paidAmount || 0 }}</view>
          </view>
        </view>
        <view class="other-info">
          <view class="operate">
            <view
              class="flex items-center justify-center btn-show"
              data-index="{{ index }}"
              bindtap="onShowPanel"
            >
              <text>{{ item.showPanel ? '收起信息' : '展开完整信息' }}</text>
              <image class="ic" src="{{ item.showPanel ? ic_arrow_up : ic_arrow_down }}" />
            </view>
          </view>
          <view wx:if="{{ item.showPanel }}" class="show-items">
            <view class="show-item">
              <view class="label">订单编号</view>
              <view class="flex items-center justify-center value">
                <view>{{ item.orderNo }}</view>
                <view
                  wx:if="{{ item.orderNo }}"
                  class="btn-copy"
                  data-item="{{ item }}"
                  bindtap="onCopyOrderNo"
                >
                  复制
                </view>
              </view>
            </view>
            <view class="show-item">
              <view class="label">支付方式</view>
              <view class="value">{{ item.payTypeText || '--' }}</view>
            </view>
            <view class="show-item">
              <view class="label">关联人员</view>
              <view class="value">{{ item.checkoutUserName || '--' }}</view>
            </view>
            <view class="show-item">
              <view class="label">交易时间</view>
              <view class="value">{{ item.checkoutTime || '--' }}</view>
            </view>
          </view>
        </view>
        <!-- <view class="foot-box">
          <view class="btn" data-item="{{ item }}" bindtap="onCancelOrder">取消订单</view>
        </view> -->
      </view>
      <load-more wx:if="{{ pageCount > 1 }}" loading="{{ isLoading }}" is-end="{{ isEnd }}" />
      <custom-empty wx:if="{{ !isLoading && list.length === 0 }}" />
    </view>
  </view>
</scroll-view>
