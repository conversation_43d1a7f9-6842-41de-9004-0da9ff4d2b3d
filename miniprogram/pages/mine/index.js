import { activityPage2, walletSummary } from '@/api/index';
import { initTabbar } from '@/utils/global';
import { encryptPhoneNumber } from '@/utils/util';
import { DEFAULT_USER_AVATAR } from '@/utils/constant';

const app = getApp();

const tabs = [
  { title: '推广中', value: 2 },
  { title: '待发布', value: 1 },
  { title: '已下架', value: 3 },
];

Page({
  data: {
    userInfo: {
      avatarUrl: DEFAULT_USER_AVATAR,
    },
    selectedTab: 0,
    tabs,
    enums: app.globalData.enums,
    drawerVisible: false,
    withdrawableBalance: '0.00',
    list: [],
    pageNo: 1,
    pageSize: 10,
    pageCount: 0,
    isLoading: false,
    isEnd: false,
    refresherStatus: false,
  },
  onLoad() {
    this.loadData();
  },
  onShow() {
    initTabbar(this, 4);
    this.initUser();
  },
  async initUser() {
    const user = app.globalData.userInfo;
    if (!user?.id) return;

    if (user) {
      const userInfo = {
        ...user,
        showMobile: encryptPhoneNumber(user.mobile),
        avatarUrl: user.headImg || user.avatarUrl || user.avatarName || DEFAULT_USER_AVATAR,
      };

      this.setData({ userInfo });
    }
  },
  async loadData(params = {}) {
    const { isEnd, isLoading } = this.data;
    if (isLoading || isEnd) return;

    const pageNo = params.pageNo || this.data.pageNo;

    if (pageNo === 1) {
      wx.showLoading();
    }
    this.setData({ isLoading: true });

    const { pageSize, selectedTab, tabs, list, pageCount } = this.data;
    const status = tabs[params?.selectedTab || selectedTab].value;

    const res = await activityPage2({
      status,
      pageNo,
      pageSize,
      ...params,
    });

    if (res.success && res.data) {
      const newList = res.data?.list || [];
      const totalCount = pageNo == 1 ? res.data.pageCount : pageCount;
      const newList2 = [{ ...newList[0], usedLimitsText: '每桌限1份' }];

      this.setData({
        list: [...newList, ...newList2],
        // list: pageNo === 1 ? newList : list.concat(newList),
        isEnd: pageNo >= totalCount,
        isLoading: false,
        pageCount: totalCount,
        pageNo,
      });
    }

    if (pageNo === 1) {
      wx.hideLoading({ noConflicat: true });
    }
  },
  // 上拉加载更多
  onReachBottom() {
    const { isEnd, pageNo } = this.data;

    if (!isEnd) {
      this.loadData({ pageNo: pageNo + 1 });
    }
  },
  onPullDownRefresh() {
    // wx.showNavigationBarLoading();
    this.setData({ pageNo: 1, pageCount: 0, isEnd: false, isLoading: false }, () => {
      this.loadData().finally(() => {
        this.setData({ refresherStatus: false });
        // wx.stopPullDownRefresh();
        // wx.hideNavigationBarLoading();
      });
    });
  },
  // onPullDownRefresh() {
  //   this.setData({ pageNo: 1, pageCount: 0, isEnd: false, isLoading: false,  }, () => {
  //     this.loadData().finally(() => {
  //       console.log('333');
  //       this.setData({ refresherStatus: false });
  //       wx.stopPullDownRefresh();
  //     });
  //   });
  // },
  async getWalletInfo() {
    const res = await walletSummary();
    if (res.success && res.data) {
      this.setData({ withdrawableBalance: Number(res.data.withdrawableBalance || 0).toFixed(2) });
    }
  },
  onTapUserInfo() {
    wx.navigateTo({
      url: '/subpages/mine/user-info/index',
    });
  },
  onTapOrderDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/subpages/activity/preview-activity/index?id=${id}`,
    });
  },
  onEditOrder(e) {
    const { id } = e.currentTarget.dataset;

    wx.navigateTo({
      url: `/subpages/activity/edit-activity/index?id=${id}`,
    });
  },
  onTapDrawerBtn() {
    const val = !this.data.drawerVisible;

    this.setData({ drawerVisible: val });
    if (val) {
      this.getWalletInfo();
    }
  },
  onDrawerChange(e) {
    const { visible } = e.detail;
    this.setData({
      drawerVisible: visible,
    });
  },
  goWallet() {
    wx.navigateTo({
      url: '/subpages/mine/wallet/index',
    });
  },
  goSetting() {
    wx.navigateTo({
      url: '/subpages/mine/setting/index',
    });
  },
  onChangeTab(e) {
    const { index } = e.detail;
    const params = { selectedTab: index, pageNo: 1 };

    this.setData({ ...params, isLoading: false, isEnd: false });
    this.loadData(params);
  },
});
