page {
  background-color: #f2f2f2;
}

.page {
  position: relative;
  padding-bottom: 230rpx;
}

.nav-left {
  height: 100%;

  .ic {
    width: 30rpx;
    height: 30rpx;
  }
}

.feedback {
  height: 100%;
  font-size: 28rpx;
}

.top-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 416rpx;
}

.top-box-wrap {
  padding: 34rpx 24rpx 24rpx;

  .top-box {
    border-bottom: 1rpx solid #999;

    .avatar-box {
      padding-bottom: 48rpx;
      .avatar {
        width: 120rpx;
        height: 120rpx;
        padding: 12rpx 18rpx 24rpx;
        box-sizing: border-box;
        margin-right: 16rpx;

        .img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          box-shadow: 0rpx 6rpx 14rpx 0rpx rgba(0, 0, 0, 0.1);
          border: 4rpx solid #ffffff;
        }

        .img2 {
          width: 100%;
          height: 100%;
        }
      }
      .title {
        .tags {
          width: 90rpx;
          height: 34rpx;
          line-height: 34rpx;
          background: rgba(84, 214, 1, 0.1);
          border-radius: 18rpx;
          font-size: 22rpx;
          color: #54d601;
          text-align: center;
          margin-left: 8rpx;
        }
      }
    }

    .ic-right {
      width: 20rpx;
      height: 20rpx;
    }
  }
}

.order-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 24rpx 20rpx;

  .order-item {
    width: 348rpx;
    border-radius: 28rpx;
    background-color: #fff;
    margin-bottom: 30rpx;

    .pic {
      position: relative;
      width: 348rpx;
      height: 348rpx;
      border-top-left-radius: 28rpx;
      border-top-right-radius: 28rpx;
      overflow: hidden;

      .img {
        width: 100%;
        height: 100%;
      }

      .status {
        position: absolute;
        bottom: 16rpx;
        left: 16rpx;
        width: 102rpx;
        height: 34rpx;
        line-height: 34rpx;
        font-size: 22rpx;
        color: #fff;
        text-align: center;
        border-radius: 18rpx;
        background: #fa5151;
      }
      .status-2 {
        background: #54d601;
      }
      .status-3 {
        background: #999;
      }
    }

    .info {
      padding: 24rpx 16rpx 0;

      .merchant-name {
        font-size: 24rpx;
        color: #333;
        line-height: 24rpx;
        margin-bottom: 16rpx;
      }

      .product-name {
        font-size: 20rpx;
        color: #999;
        line-height: 20rpx;
      }

      .tips {
        margin: 16rpx 0 0;
        font-size: 20rpx;
        color: #999;
        line-height: 24rpx;
        height: 48rpx;
        max-height: 48rpx;
        margin-bottom: 8rpx;
        overflow: hidden;
        // max-lines: 2 !important;
      }

      .price {
        display: flex;
        align-items: flex-end;
        color: #fa5151;
        line-height: 32rpx;

        .symbol {
          font-weight: 400;
          font-size: 24rpx;
          line-height: 24rpx;
        }

        .val {
          font-weight: 500;
          font-size: 32rpx;
        }
      }
    }
    .btn-edit {
      width: 44rpx;
      height: 22rpx;
      font-size: 22rpx;
      color: #333333;
      line-height: 22rpx;
      padding: 24rpx 16rpx;
    }
  }
}

.drawer-box {
  width: 564rpx;
  background: #354829;
  padding-top: 40rpx;

  .drawer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 120rpx;
    padding: 0 48rpx;
    .left {
      display: flex;
      align-items: center;
      .ic {
        width: 36rpx;
        height: 36rpx;
        margin-right: 16rpx;
      }
      .text {
        height: 32rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #ffffff;
        line-height: 32rpx;
      }
    }
    .right {
      display: flex;
      align-items: center;
      .ic {
        width: 20rpx;
        height: 20rpx;
      }
      .text {
        height: 32rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #ffffff;
        line-height: 32rpx;
        margin-right: 16rpx;
      }
    }
  }
}
