<scroll-view
  scroll-y
  style="flex: 1"
  show-scrollbar="{{ false }}"
  enhanced
  refresher-enabled
  refresher-triggered="{{ refresherStatus }}"
  bindrefresherrefresh="onPullDownRefresh"
  bindscrolltolower="onReachBottom"
>
  <view class="page">
    <image class="top-bg" src="/assets/image/bg/top-gradient.png" />
    <t-navbar title="" class="custom-navbar-transparent">
      <view slot="left" class="flex items-center nav-left" bindtap="onTapDrawerBtn">
        <image class="ic" src="/assets/image/icon/drawer.png" />
      </view>
    </t-navbar>

    <view class="top-box-wrap">
      <view class="flex items-center justify-between top-box" bindtap="onTapUserInfo">
        <view class="flex items-center avatar-box">
          <view class="avatar">
            <custom-image
              class="img"
              src="{{ userInfo.headImg || userInfo.avatarUrl }}"
              radius="50%"
            />
          </view>
          <view wx:if="{{ userInfo.id }}" class="title">
            <view class="flex items-center">
              <text>{{ userInfo.fullName || userInfo.nickname }}</text>
              <text class="tags">管理员</text>
            </view>
            <view class="mobile">{{ userInfo.showMobile }}</view>
          </view>
          <view wx:else class="title" bindtap="onTapLogin">登录</view>
        </view>

        <image class="ic-right" src="/assets/image/icon/arrow-right.png" />
      </view>
    </view>

    <sky-tab
      title="选项卡样式"
      tabList="{{ tabs }}"
      tab-color="#999"
      select-color="#333"
      select-blod="true"
      select-type="line"
      select-bg="#54d601"
      tab-current="{{ selectedTab }}"
      ontouchTab="onChangeTab"
    />

    <view class="order-list">
      <view
        wx:for="{{ list }}"
        wx:key="id"
        class="order-item"
        data-id="{{ item.id }}"
        catchtap="onEditOrder"
      >
        <!-- bindtap="onTapOrderDetail" -->
        <view class="pic">
          <custom-image class="img" src="{{ item.imageUrl }}" />
          <view class="status status-{{ item.status }}">
            {{ item.statusText }}
          </view>
        </view>
        <view class="info">
          <!-- prettier-ignore -->
          <text class="truncate merchant-name">{{ item.title }} *{{ item.quantity }}{{ enums.ActUnits[item.units] }}</text>
          <!-- <view class="product-name">{{ item.productName }} *{{ item.num }}份</view> -->
          <!-- prettier-ignore -->
          <text class="tips">{{ item.usedLimitsText }}</text>
          <!-- <view class="price">
            <view class="symbol">¥</view>
            <view class="val">{{ item.activityPrice }}</view>
          </view> -->
        </view>
        <view class="btn">
          <view class="btn-edit" data-id="{{ item.id }}">编辑</view>
        </view>
      </view>
    </view>
    <!-- 上拉加载组件 -->
    <load-more wx:if="{{ pageCount > 1 }}" loading="{{ isLoading }}" is-end="{{ isEnd }}" />
    <custom-empty wx:if="{{ !isLoading && list.length === 0 }}" />
  </view>
</scroll-view>

<t-popup
  visible="{{ drawerVisible }}"
  usingCustomNavbar
  using-custom-navbar
  default-visible
  bind:visible-change="onDrawerChange"
  placement="left"
  style="background-color: #354829"
>
  <view class="drawer-box">
    <view class="drawer-item" bindtap="goWallet">
      <view class="left">
        <image class="ic" src="/assets/image/icon/wallet.png" />
        <text class="text">钱包</text>
      </view>
      <view class="right">
        <text class="text">{{ withdrawableBalance || '0.00' }}</text>
        <image class="ic" src="/assets/image/icon/arrow-right2.png" />
      </view>
    </view>
    <view class="drawer-item" bindtap="goSetting">
      <view class="left">
        <image class="ic" src="/assets/image/icon/set.png" />
        <text class="text">设置</text>
      </view>
      <view class="right">
        <image class="ic" src="/assets/image/icon/arrow-right2.png" />
      </view>
    </view>
  </view>
</t-popup>
