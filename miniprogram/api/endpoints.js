/**
 * 权限相关
 */
const auth = {
  codeLogin: '/sys/weixinMini/platformCodeLogin', // 平台管理员小程序code登录
  register: '/sys/weixinMini/platformBindLogin', // 平台管理员(小程序)绑定并登录(手机号+验证码)
  //
  loginCheck: '/sys/login/check', // 手机号验证码登录
  logout: '/sys/login/logout',
  //
  sendVerifyCode: '/common/sms/sendVerifyCode', // 发送验证码
  verifyCode: '/common/sms/verifyCode', // 验证手机验证码
  sendVerifyCode2: '/sys/user/sendVerifyCode2', // 发送验证码-更换手机号
};

// ----------------------------------------------------------------------

/**
 * 用户相关
 */
const user = {
  changeMobile: '/sys/user/changeMobile', // 保存更换手机号
  sendVerifyCode: '/sys/user/sendVerifyCode2', // 发送验证码-更换手机号
  userUpdate: '/sys/user/userUpdate', // 保存用户修改信息
};

// ----------------------------------------------------------------------

/**
 * 首页
 */
const home = {
  homeSummary: '/oms/orderStatistics/homeSummary', // 首页-汇总数据
  summaryShopList: '/oms/orderStatistics/summaryShopList', // 首页-已锁客(订单门店汇总)列表
  shopSummaryOrderDetail: '/oms/orderStatistics/shopSummaryOrderDetail', // 首页-已锁客(订单门店汇总)详情
  summaryConsumeShopList: '/oms/orderStatistics/summaryConsumeShopList', // 首页-核销量列表
  shopList: '/oms/orderStatistics/shopList', // 首页-新开店列表
  shopDetail: '/oms/orderStatistics/shopDetail', // 首页-新开店详情
  shopuUserRewordList: '/oms/orderStatistics/shopuUserRewordList', // 首页-店员奖励列表
  shopuUserRewordDetail: '/oms/orderStatistics/shopuUserRewordDetail', // 首页-店员奖励详情
  shopuUserPendingRewordList: '/oms/orderStatistics/shopuUserPendingRewordList', // 首页-店员待奖励列表
  shopuUserPendingRewordDetail: '/oms/orderStatistics/shopuUserPendingRewordDetail', // 首页-店员待奖励详情
  summaryConsumeShopDetail: '/oms/orderStatistics/summaryConsumeShopDetail', // 首页-核销量详情
};

/**
 *  门店
 */
const shop = {
  businessCategoryList: '/shop/businessCategory/list', // 经营品类列表
  businessCategoryListAll: '/shop/businessCategory/listAll', // 全部-经营品类列表
  createShop: '/shop/shopInfo/create', // 新建门店
  shopInfoDetail: '/shop/shopInfo/detail', // 门店详情
  updateShop: '/shop/shopInfo/update', // 修改门店
  shopPage: '/shop/shopInfo/page', // 门店列表
};

// ----------------------------------------------------------------------

/**
 *  活动管理
 */
const activity = {
  activityPage: '/act/activity/page', // 活动列表
  activityPage2: '/act/activity/page2', // 我的-活动列表
  activityCreate: '/act/activity/create', // 新建活动
  activityUpdate: '/act/activity/update', // 修改活动
  activityDetail: '/act/activity/detail', // 活动详情-for修改
  activityDetail2: '/act/activity/detail2', // 活动详情-for显示
  checkShopOnlineActivity: '/act/activity/checkShopOnlineActivity', // 检查门店是否已经有推广中的活动
  changeStatus: '/act/activity/changeStatus', // 修改活动(上架、下架、发布)
};

// ----------------------------------------------------------------------

/**
 *  平台
 */
const platform = {
  // 钱包汇总信息
  walletSummary: '/platform/salesWallet/summary',
  // 提现列表
  withdrawalList: '/platform/salesWallet/withdrawalList',
  // 提现明细
  withdrawalDetail: '/platform/salesWallet/withdrawalDetail',
  // 待奖励列表
  pendingRewardList: '/platform/salesWallet/pendingRewardList',
  // 待奖励详情
  pendingRewardDetail: '/platform/salesWallet/pendingRewardDetail',
  // 余额列表
  balanceList: '/platform/salesWallet/balanceList',
  // 余额明细
  balanceDetail: '/platform/salesWallet/balanceDetail',
};
// ----------------------------------------------------------------------

/**
 * 订单
 */
const order = {
  // 订单-订单列表
  orderPage: '/con/consume/orderPage',
};

// ----------------------------------------------------------------------
/**
 * 公共
 */
const common = {
  uploadImage: '/common/upload/image',
  allEnumList: '/common/enum/listAll',
  allAddressList: '/common/address/listAll',
};

// ----------------------------------------------------------------------

export default { auth, home, user, shop, activity, platform, order, common };
