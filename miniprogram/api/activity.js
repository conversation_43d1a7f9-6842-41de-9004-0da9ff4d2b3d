import request from '@/utils/request';
import endpoints from './endpoints';

// ------------------------------------------------------------------------------------------
const { activity } = endpoints;

// 活动列表
export const activityList = (d) => request.post(activity.activityPage, d);
// 新建活动
export const createActivity = (d) => request.post(activity.activityCreate, d);
// 修改活动
export const updateActivity = (d) => request.post(activity.activityUpdate, d);
// 我的-活动列表
export const activityPage2 = (d) => request.post(activity.activityPage2, d);
// 活动详情-for修改
export const activityDetail = (d) => request.post(activity.activityDetail, d);
// 活动详情-for显示
export const activityDetail2 = (d) => request.post(activity.activityDetail2, d);
// 检查门店是否已经有推广中的活动
export const checkShopOnlineActivity = (d) => request.post(activity.checkShopOnlineActivity, d);
// 修改活动(上架、下架、发布)
export const changeStatus = (d) => request.post(activity.changeStatus, d);
