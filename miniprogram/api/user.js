import request from '@/utils/request';
import endpoints from './endpoints';

// ------------------------------------------------------------------------------------------

const { auth, user } = endpoints;

// code登录
export const codeLogin = (d) => request.post(auth.codeLogin, d);
// 验证码登录/注册
export const platformBindLogin = (d) => request.post(auth.register, d);

// 手机密码登录
export const loginCheck = (d) => request.post(auth.loginCheck, d);

// ------------------------------------------------------------------------------------------

// 发送验证码
export const sendVerifyCode = (d) => request.post(auth.sendVerifyCode, d);
// 发送验证码-更换手机号
export const sendVerifyCode2 = (data) => request.post(auth.sendVerifyCode2, data);

// 保存用户修改信息
export const userUpdate = (data) => request.post(user.userUpdate, data);
// 保存更换手机号
export const changeMobile = (data) => request.post(user.changeMobile, data);
