import request from '@/utils/request';
import endpoints from './endpoints';

// ------------------------------------------------------------------------------------------

const { home } = endpoints;

// 首页统计
export const homeSummary = (d) => request.post(home.homeSummary, d);
// 首页-已锁客(订单门店汇总)列表
export const summaryShopList = (d) => request.post(home.summaryShopList, d);
// 首页-已锁客(订单门店汇总)详情
export const shopSummaryOrderDetail = (d) => request.post(home.shopSummaryOrderDetail, d);
// 首页-核销量列表
export const summaryConsumeShopList = (d) => request.post(home.summaryConsumeShopList, d);
// 首页-新开店列表
export const shopList = (d) => request.post(home.shopList, d);
// 首页-新开店详情
export const shopDetail = (d) => request.post(home.shopDetail, d);
// 首页-店员奖励列表
export const shopuUserRewordList = (d) => request.post(home.shopuUserRewordList, d);
// 首页-店员奖励详情
export const shopuUserRewordDetail = (d) => request.post(home.shopuUserRewordDetail, d);
// 首页-店员待奖励列表
export const shopuUserPendingRewordList = (d) => request.post(home.shopuUserPendingRewordList, d);
// 首页-店员待奖励详情
export const shopuUserPendingRewordDetail = (d) => request.post(home.shopuUserPendingRewordDetail, d);
// 首页-核销量详情
export const summaryConsumeShopDetail = (d) => request.post(home.summaryConsumeShopDetail, d);