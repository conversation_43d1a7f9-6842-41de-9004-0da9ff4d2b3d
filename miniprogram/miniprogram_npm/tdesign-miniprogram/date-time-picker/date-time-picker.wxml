<wxs src="../common/utils.wxs" module="_" />
<t-picker
  style="{{_._style([style, customStyle])}}"
  class="class {{prefix}}-class {{classPrefix}}"
  visible="{{visible}}"
  value="{{columnsValue}}"
  header="{{header}}"
  title="{{title}}"
  auto-close="{{autoClose}}"
  confirm-btn="{{confirmBtn || locale.confirm}}"
  cancel-btn="{{cancelBtn || locale.cancel}}"
  use-popup="{{usePopup}}"
  popup-props="{{ popupProps }}"
  bind:pick="onColumnChange"
  bind:change="onConfirm"
  bind:cancel="onCancel"
  bind:visible-change="onVisibleChange"
  bind:close="onClose"
>
  <slot slot="header" name="header" />
  <t-picker-item
    wx:for="{{columns}}"
    wx:key="index"
    class="{{_.cls(classPrefix + '__item', [['roomly', columns.length > 5 && index == 0]])}}"
    options="{{item}}"
    index="index"
  />
  <slot slot="footer" name="footer" />
</t-picker>
