<wxs src="../common/utils.wxs" module="_" />

<view
  id="{{tId}}"
  style="{{_._style([style, customStyle])}}"
  class="{{_.cls(classPrefix, [placement, theme, ['checked', checked], ['block', block]])}} class {{prefix}}-class"
  aria-role="checkbox"
  aria-checked="{{checked ? (indeterminate ? 'mixed' : true) : false}}"
  aria-disabled="{{_disabled ? true : false}}"
  mut-bind:tap="handleTap"
  tabindex="{{tabindex}}"
>
  <view
    wx:if="{{theme == 'default'}}"
    class="{{_.cls(classPrefix + '__icon', [placement, ['checked', checked], ['disabled', _disabled]])}} {{prefix}}-class-icon"
  >
    <slot name="icon" wx:if="{{icon === 'slot'}}" />
    <view wx:elif="{{_.isArray(icon)}}" class="{{classPrefix}}__icon">
      <image
        src="{{checked ? indeterminate && icon[2] ? icon[2] : icon[0] : icon[1]}}"
        class="{{classPrefix}}__icon-image"
        webp
      />
    </view>
    <block wx:else>
      <t-icon
        wx:if="{{checked && (icon == 'circle' || icon == 'rectangle')}}"
        name="{{indeterminate ? ('minus-' + icon + '-filled') : ('check-' + icon + '-filled')}}"
        class="{{_.cls(classPrefix + '__icon-wrapper', [])}}"
      />
      <t-icon
        wx:if="{{checked && icon == 'line'}}"
        name="{{indeterminate ? ('minus-' + icon + '-filled') : 'check'}}"
        class="{{_.cls(classPrefix + '__icon-wrapper', [])}}"
      />
      <view
        wx:elif="{{!checked && (icon == 'circle' || icon == 'rectangle')}}"
        class="{{_.cls(classPrefix + '__icon-' + icon, [['disabled', _disabled]])}}"
      />
      <view wx:if="{{!checked && icon == 'line'}}" class="placeholder"></view>
    </block>
  </view>
  <view class="{{classPrefix}}__content" data-target="text" mut-bind:tap="handleTap">
    <view
      class="{{_.cls(classPrefix + '__title', [['disabled', _disabled], ['checked', checked]])}} {{prefix}}-class-label"
      style="-webkit-line-clamp:{{maxLabelRow}}"
    >
      <block wx:if="{{label}}">{{label}}</block>
      <slot />
      <slot name="label" />
    </view>
    <view
      class="{{_.cls(classPrefix + '__description', [['disabled', _disabled]])}} {{prefix}}-class-content "
      style="-webkit-line-clamp:{{maxContentRow}}"
    >
      <block wx:if="{{content}}">{{content}}</block>
      <slot name="content" />
    </view>
  </view>
  <view
    wx:if="{{theme == 'default' && !borderless}}"
    class="{{_.cls(classPrefix + '__border', [placement])}} {{prefix}}-class-border"
  />
</view>
