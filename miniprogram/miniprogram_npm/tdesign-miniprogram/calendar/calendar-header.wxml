<wxs src="../common/utils.wxs" module="_" />

<template name="calendar-header">
  <view class="{{class}} {{classPrefix}} {{switchMode !== 'none' ? classPrefix + '__with-action' : ''}}" id="{{tId}}">
    <view class="{{classPrefix}}__action" wx:if="{{switchMode !== 'none'}}">
      <view
        wx:if="{{switchMode === 'year-month'}}"
        class="{{_.cls(classPrefix + '__icon', [['disabled', preYearBtnDisable]])}}"
        data-disabled="{{preYearBtnDisable}}"
        data-type="pre-year"
        bindtap="handleSwitchModeChange"
      >
        <t-icon name="chevron-left-double" />
      </view>
      <view
        class="{{_.cls(classPrefix + '__icon', [['disabled', prevMonthBtnDisable]])}}"
        data-disabled="{{prevMonthBtnDisable}}"
        data-type="pre-month"
        bindtap="handleSwitchModeChange"
      >
        <t-icon name="chevron-left" />
      </view>
    </view>
    <view class="{{classPrefix}}__title"> {{ title }}</view>
    <view class="{{classPrefix}}__action" wx:if="{{switchMode !== 'none'}}">
      <view
        class="{{_.cls(classPrefix + '__icon', [['disabled', nextMonthBtnDisable]])}}"
        data-disabled="{{nextMonthBtnDisable}}"
        data-type="next-month"
        bindtap="handleSwitchModeChange"
      >
        <t-icon name="chevron-right" />
      </view>
      <view
        wx:if="{{switchMode === 'year-month'}}"
        class="{{_.cls(classPrefix + '__icon', [['disabled', nextYearBtnDisable]])}}"
        data-disabled="{{nextYearBtnDisable}}"
        data-type="next-year"
        bindtap="handleSwitchModeChange"
      >
        <t-icon name="chevron-right-double" />
      </view>
    </view>
  </view>
</template>
