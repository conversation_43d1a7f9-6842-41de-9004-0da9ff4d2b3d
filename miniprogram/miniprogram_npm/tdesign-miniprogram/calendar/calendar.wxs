function getDateLabel(monthItem, dateItem) {
  var weekdayText = ['日', '一', '二', '三', '四', '五', '六'];
  var weekday = (monthItem.weekdayOfFirstDay + dateItem.day - 1) % 7;
  var label = monthItem.month + 1 + '月' + dateItem.day + '日, 星期' + weekdayText[weekday];
  if (dateItem.type === 'start') {
    label = '开始日期：' + label;
  }
  if (dateItem.type === 'end') {
    label = '结束日期：' + label;
  }
  if (isDateSelected(dateItem)) {
    label = '已选中, ' + label;
  }
  if (dateItem.prefix) {
    label += ', ' + dateItem.prefix;
  }
  if (dateItem.suffix) {
    label += ', ' + dateItem.suffix;
  }
  return label;
}

function isDateSelected(dateItem) {
  return ['start', 'end', 'selected', 'centre'].indexOf(dateItem.type) >= 0;
}

function getMonthTitle(year, month, pattern = '') {
  // prettier-ignore
  var REGEXP = getRegExp('\{year\}|\{month\}', 'g');

  return pattern.replace(REGEXP, function (match) {
    var replacements = {
      '{year}': year,
      '{month}': month < 10 ? '0' + month : month,
    };
    return replacements[match] || match;
  });
}

module.exports = {
  getDateLabel: getDateLabel,
  isDateSelected: isDateSelected,
  getMonthTitle: getMonthTitle,
};
