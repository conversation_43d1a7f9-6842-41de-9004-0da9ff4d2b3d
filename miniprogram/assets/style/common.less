/* Flexbox 布局 */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-baseline {
  align-items: baseline;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}

// 字体
.text-ellipsis {
  text-overflow: ellipsis;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// /* 字体和排版 */
// .text-sm {
//   font-size: 12px;
// }
// .text-base {
//   font-size: 16px;
// }
// .text-lg {
//   font-size: 18px;
// }
// .text-xl {
//   font-size: 20px;
// }
// .font-bold {
//   font-weight: bold;
// }
// .font-medium {
//   font-weight: 500;
// }
// .font-normal {
//   font-weight: normal;
// }
// .text-left {
//   text-align: left;
// }
// .text-center {
//   text-align: center;
// }
// .text-right {
//   text-align: right;
// }
// .leading-5 {
//   line-height: 1.25rem; /* 20px */
// }
// .leading-7 {
//   line-height: 1.75rem; /* 28px */
// }

// /* 颜色 */

// /* 根据你预设的颜色调整， 这里演示几个，你需要自己扩展 */
// .bg-gray-100 {
//   background-color: #f7fafc;
// }
// .bg-blue-500 {
//   background-color: #3b82f6;
// }
// .bg-blue-100 {
//   background-color: #edf5fc;
// }
// .text-red-500 {
//   color: #ef4444;
// }
// .text-blue-800 {
//   color: #1e3a8a;
// }
// .text-blue-600 {
//   color: #2563eb;
// }
// .text-gray-800 {
//   color: #1f2937;
// }
// .text-gray-600 {
//   color: #4b5563;
// }
// .text-gray-500 {
//   color: #6b7280;
// }

// /* 边距和间距 */
// .m-1 {
//   margin: 4rpx;
// }
// .m-2 {
//   margin: 8rpx;
// }
// .m-3 {
//   margin: 12rpx;
// }
// .m-4 {
//   margin: 16rpx;
// }
// .mt-2 {
//   margin-top: 8rpx;
// }
// .mb-2 {
//   margin-bottom: 8rpx;
// }
// .ml-2 {
//   margin-left: 8rpx;
// }
// .mr-2 {
//   margin-right: 8rpx;
// }
// .p-1 {
//   padding: 4rpx;
// }
// .p-2 {
//   padding: 8rpx;
// }
// .p-3 {
//   padding: 12rpx;
// }
// .p-4 {
//   padding: 16rpx;
// }
// .pt-2 {
//   padding-top: 8rpx;
// }
// .pb-2 {
//   padding-bottom: 8rpx;
// }
// .pl-2 {
//   padding-left: 8rpx;
// }
// .pr-2 {
//   padding-right: 8rpx;
// }
// .space-x-2 > *:not(:first-child) {
//   margin-left: 8rpx;
// }
// .space-y-2 > *:not(:first-child) {
//   margin-top: 8rpx;
// }

// /* 边框 */
// .border {
//   border-width: 1px;
//   border-style: solid;
// }
// .border-2 {
//   border-width: 2px;
//   border-style: solid;
// }
// .border-gray-200 {
//   border-color: #e5e7eb;
// }
// .border-gray-300 {
//   border-color: #d1d5db;
// }
// .rounded {
//   border-radius: 4px;
// }
// .rounded-md {
//   border-radius: 6px;
// }
// .rounded-lg {
//   border-radius: 8px;
// }
// .rounded-xl {
//   border-radius: 12px;
// }

// /* 宽度 */
// .w-full {
//   width: 100%;
// }
// .h-full {
//   height: 100%;
// }

// /* 定位 */
// .relative {
//   position: relative;
// }
// .absolute {
//   position: absolute;
// }
// .fixed {
//   position: fixed;
// }

// /* 溢出 */
// .overflow-hidden {
//   overflow: hidden;
// }
// .overflow-x-hidden {
//   overflow-x: hidden;
// }
// .overflow-y-hidden {
//   overflow-y: hidden;
// }

// /* 阴影 */
// .shadow {
//   box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
// }
// .shadow-md {
//   box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
// }
// .shadow-lg {
//   box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
// }

// /* 按钮 */
// .bg-blue-500 {
//   background-color: #3b82f6;
// }
// .hover\:bg-blue-700:hover {
//   background-color: #1d4ed8;
// }
// .bg-green-500 {
//   background-color: #22c55e;
// }
// .hover\:bg-green-700:hover {
//   background-color: #16a34a;
// }
// .text-white {
//   color: #ffffff;
// }
// .font-bold {
//   font-weight: bold;
// }
// .py-2 {
//   padding-top: 8rpx;
//   padding-bottom: 8rpx;
// }
// .px-4 {
//   padding-left: 16rpx;
//   padding-right: 16rpx;
// }
// .rounded {
//   border-radius: 4px;
// }
