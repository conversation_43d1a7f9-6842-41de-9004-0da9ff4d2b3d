@font-face {
  font-family: "iconfont"; /* Project id 4326186 */
  src: url('https://at.alicdn.com/t/c/font_4326186_ruiarwog8sf.ttf?t=1700450041676') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@font-face {
  font-family: "numberfont"; 
  src: url('https://sky-ui-1300145561.cos.ap-chengdu.myqcloud.com/DIN-Regular-2.otf') format('truetype');
}

.numberfont {
  font-family: "numberfont" !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-down:before {
  content: "\e7c8";
}

.icon-at-sign:before {
  content: "\e7c9";
}

.icon-arrow-up-left:before {
  content: "\e7ca";
}

.icon-aperture:before {
  content: "\e7cb";
}

.icon-alert-octagon:before {
  content: "\e7cc";
}

.icon-align-right:before {
  content: "\e7cd";
}

.icon-alert-triangle:before {
  content: "\e7ce";
}

.icon-airplay:before {
  content: "\e7cf";
}

.icon-activity:before {
  content: "\e7d0";
}

.icon-align-justify:before {
  content: "\e7d1";
}

.icon-chevron-down:before {
  content: "\e7b6";
}

.icon-anchor:before {
  content: "\e7b7";
}

.icon-bookmark:before {
  content: "\e7b8";
}

.icon-bar-chart:before {
  content: "\e7b9";
}

.icon-camera-off:before {
  content: "\e7ba";
}

.icon-cloud-lightning:before {
  content: "\e7bb";
}

.icon-briefcase:before {
  content: "\e7bc";
}

.icon-chevrons-left:before {
  content: "\e7bd";
}

.icon-arrow-down-right:before {
  content: "\e7be";
}

.icon-arrow-up:before {
  content: "\e7bf";
}

.icon-chevrons-up:before {
  content: "\e7c0";
}

.icon-camera:before {
  content: "\e7c1";
}

.icon-arrow-down-left:before {
  content: "\e7c2";
}

.icon-check-circle:before {
  content: "\e7c3";
}

.icon-battery:before {
  content: "\e7c4";
}

.icon-archive:before {
  content: "\e7c5";
}

.icon-check:before {
  content: "\e7c6";
}

.icon-arrow-left:before {
  content: "\e7c7";
}

.icon-database:before {
  content: "\e7a3";
}

.icon-box:before {
  content: "\e7a4";
}

.icon-cloud-drizzle:before {
  content: "\e7a5";
}

.icon-bluetooth:before {
  content: "\e7a6";
}

.icon-circle:before {
  content: "\e7a7";
}

.icon-bell-off:before {
  content: "\e7a8";
}

.icon-align-left:before {
  content: "\e7a9";
}

.icon-code:before {
  content: "\e7aa";
}

.icon-arrow-right-circle:before {
  content: "\e7ab";
}

.icon-bar-chart-2:before {
  content: "\e7ac";
}

.icon-align-center:before {
  content: "\e7ad";
}

.icon-compass:before {
  content: "\e7ae";
}

.icon-bell:before {
  content: "\e7af";
}

.icon-arrow-down-circle:before {
  content: "\e7b0";
}

.icon-corner-down-left:before {
  content: "\e7b1";
}

.icon-chevron-right:before {
  content: "\e7b2";
}

.icon-arrow-right:before {
  content: "\e7b3";
}

.icon-corner-up-left:before {
  content: "\e7b4";
}

.icon-chrome:before {
  content: "\e7b5";
}

.icon-coffee:before {
  content: "\e79f";
}

.icon-cast:before {
  content: "\e7a0";
}

.icon-corner-right-down:before {
  content: "\e7a1";
}

.icon-check-square:before {
  content: "\e7a2";
}

.icon-arrow-left-circle:before {
  content: "\e6b4";
}

.icon-help-circle:before {
  content: "\e792";
}

.icon-corner-down-right:before {
  content: "\e793";
}

.icon-clipboard:before {
  content: "\e794";
}

.icon-award:before {
  content: "\e795";
}

.icon-edit-3:before {
  content: "\e796";
}

.icon-alert-circle:before {
  content: "\e6b3";
}

.icon-arrow-up-right:before {
  content: "\e797";
}

.icon-disc:before {
  content: "\e798";
}

.icon-cloud:before {
  content: "\e799";
}

.icon-command:before {
  content: "\e79a";
}

.icon-book:before {
  content: "\e79b";
}

.icon-globe:before {
  content: "\e79c";
}

.icon-codesandbox:before {
  content: "\e79d";
}

.icon-external-link:before {
  content: "\e79e";
}

.icon-divide-circle:before {
  content: "\e77f";
}

.icon-eye:before {
  content: "\e780";
}

.icon-frown:before {
  content: "\e781";
}

.icon-gitlab:before {
  content: "\e782";
}

.icon-battery-charging:before {
  content: "\e6b5";
}

.icon-file:before {
  content: "\e783";
}

.icon-edit:before {
  content: "\e784";
}

.icon-divide-square:before {
  content: "\e785";
}

.icon-bold:before {
  content: "\e786";
}

.icon-divide:before {
  content: "\e787";
}

.icon-chevron-left:before {
  content: "\e788";
}

.icon-copy:before {
  content: "\e789";
}

.icon-columns:before {
  content: "\e78a";
}

.icon-crosshair:before {
  content: "\e78b";
}

.icon-corner-left-up:before {
  content: "\e78c";
}

.icon-home:before {
  content: "\e78d";
}

.icon-chevrons-right:before {
  content: "\e78e";
}

.icon-file-minus:before {
  content: "\e78f";
}

.icon-clock:before {
  content: "\e790";
}

.icon-book-open:before {
  content: "\e791";
}

.icon-github:before {
  content: "\e76e";
}

.icon-feather:before {
  content: "\e76f";
}

.icon-meh:before {
  content: "\e770";
}

.icon-file-text:before {
  content: "\e771";
}

.icon-codepen:before {
  content: "\e772";
}

.icon-layers:before {
  content: "\e773";
}

.icon-link:before {
  content: "\e774";
}

.icon-cpu:before {
  content: "\e775";
}

.icon-lock:before {
  content: "\e776";
}

.icon-credit-card:before {
  content: "\e6b6";
}

.icon-cloud-off:before {
  content: "\e777";
}

.icon-linkedin:before {
  content: "\e778";
}

.icon-film:before {
  content: "\e779";
}

.icon-chevrons-down:before {
  content: "\e77a";
}

.icon-folder:before {
  content: "\e77b";
}

.icon-cloud-rain:before {
  content: "\e77c";
}

.icon-chevron-up:before {
  content: "\e77d";
}

.icon-download-cloud:before {
  content: "\e77e";
}

.icon-link-2:before {
  content: "\e75a";
}

.icon-hard-drive:before {
  content: "\e75b";
}

.icon-eye-off:before {
  content: "\e75c";
}

.icon-minus-circle:before {
  content: "\e75d";
}

.icon-hexagon:before {
  content: "\e75e";
}

.icon-more-horizontal:before {
  content: "\e75f";
}

.icon-filter:before {
  content: "\e760";
}

.icon-loader:before {
  content: "\e761";
}

.icon-facebook:before {
  content: "\e762";
}

.icon-navigation:before {
  content: "\e763";
}

.icon-git-pull-request:before {
  content: "\e764";
}

.icon-droplet:before {
  content: "\e765";
}

.icon-edit-2:before {
  content: "\e766";
}

.icon-calendar:before {
  content: "\e767";
}

.icon-corner-right-up:before {
  content: "\e768";
}

.icon-git-commit:before {
  content: "\e769";
}

.icon-hash:before {
  content: "\e76a";
}

.icon-instagram:before {
  content: "\e76b";
}

.icon-dollar-sign:before {
  content: "\e76c";
}

.icon-minimize:before {
  content: "\e76d";
}

.icon-paperclip:before {
  content: "\e74c";
}

.icon-minus-square:before {
  content: "\e74d";
}

.icon-git-branch:before {
  content: "\e74e";
}

.icon-crop:before {
  content: "\e74f";
}

.icon-corner-left-down:before {
  content: "\e750";
}

.icon-headphones:before {
  content: "\e751";
}

.icon-radio:before {
  content: "\e752";
}

.icon-monitor:before {
  content: "\e753";
}

.icon-move:before {
  content: "\e754";
}

.icon-navigation-2:before {
  content: "\e755";
}

.icon-flag:before {
  content: "\e756";
}

.icon-moon:before {
  content: "\e757";
}

.icon-cloud-snow:before {
  content: "\e758";
}

.icon-figma:before {
  content: "\e759";
}

.icon-git-merge:before {
  content: "\e738";
}

.icon-message-circle:before {
  content: "\e739";
}

.icon-server:before {
  content: "\e73a";
}

.icon-pocket:before {
  content: "\e73b";
}

.icon-info:before {
  content: "\e73c";
}

.icon-map-pin:before {
  content: "\e73d";
}

.icon-log-out:before {
  content: "\e73e";
}

.icon-power:before {
  content: "\e73f";
}

.icon-dribbble:before {
  content: "\e740";
}

.icon-phone-incoming:before {
  content: "\e741";
}

.icon-folder-minus:before {
  content: "\e742";
}

.icon-search:before {
  content: "\e743";
}

.icon-delete:before {
  content: "\e744";
}

.icon-phone-forwarded:before {
  content: "\e745";
}

.icon-phone-call:before {
  content: "\e746";
}

.icon-pen-tool:before {
  content: "\e747";
}

.icon-folder-plus:before {
  content: "\e748";
}

.icon-plus-circle:before {
  content: "\e749";
}

.icon-map:before {
  content: "\e74a";
}

.icon-fast-forward:before {
  content: "\e74b";
}

.icon-phone-missed:before {
  content: "\e723";
}

.icon-save:before {
  content: "\e724";
}

.icon-repeat:before {
  content: "\e725";
}

.icon-gift:before {
  content: "\e726";
}

.icon-octagon:before {
  content: "\e727";
}

.icon-phone-outgoing:before {
  content: "\e728";
}

.icon-more-vertical:before {
  content: "\e729";
}

.icon-minimize-2:before {
  content: "\e72a";
}

.icon-sun:before {
  content: "\e72b";
}

.icon-play-circle:before {
  content: "\e72c";
}

.icon-shopping-cart:before {
  content: "\e72d";
}

.icon-sliders:before {
  content: "\e72e";
}

.icon-grid:before {
  content: "\e72f";
}

.icon-mic:before {
  content: "\e730";
}

.icon-menu:before {
  content: "\e731";
}

.icon-log-in:before {
  content: "\e732";
}

.icon-key:before {
  content: "\e733";
}

.icon-shield-off:before {
  content: "\e734";
}

.icon-star:before {
  content: "\e735";
}

.icon-sidebar:before {
  content: "\e736";
}

.icon-minus:before {
  content: "\e737";
}

.icon-underline:before {
  content: "\e711";
}

.icon-skip-forward:before {
  content: "\e712";
}

.icon-file-plus:before {
  content: "\e6b8";
}

.icon-speaker:before {
  content: "\e713";
}

.icon-shield:before {
  content: "\e714";
}

.icon-type:before {
  content: "\e715";
}

.icon-umbrella:before {
  content: "\e716";
}

.icon-sunset:before {
  content: "\e717";
}

.icon-tv:before {
  content: "\e718";
}

.icon-percent:before {
  content: "\e719";
}

.icon-trello:before {
  content: "\e71a";
}

.icon-slash:before {
  content: "\e71b";
}

.icon-user-plus:before {
  content: "\e71c";
}

.icon-mic-off:before {
  content: "\e6ba";
}

.icon-arrow-up-circle:before {
  content: "\e71d";
}

.icon-refresh-ccw:before {
  content: "\e71e";
}

.icon-corner-up-right:before {
  content: "\e6b7";
}

.icon-rotate-ccw:before {
  content: "\e71f";
}

.icon-life-buoy:before {
  content: "\e720";
}

.icon-toggle-left:before {
  content: "\e721";
}

.icon-toggle-right:before {
  content: "\e722";
}

.icon-download:before {
  content: "\e6b9";
}

.icon-triangle:before {
  content: "\e6fc";
}

.icon-trending-down:before {
  content: "\e6fd";
}

.icon-zoom-in:before {
  content: "\e6fe";
}

.icon-inbox:before {
  content: "\e6ff";
}

.icon-plus:before {
  content: "\e700";
}

.icon-tag:before {
  content: "\e701";
}

.icon-thumbs-up:before {
  content: "\e702";
}

.icon-square:before {
  content: "\e703";
}

.icon-heart:before {
  content: "\e704";
}

.icon-upload-cloud:before {
  content: "\e705";
}

.icon-terminal:before {
  content: "\e706";
}

.icon-table:before {
  content: "\e707";
}

.icon-image:before {
  content: "\e708";
}

.icon-rotate-cw:before {
  content: "\e709";
}

.icon-list:before {
  content: "\e70a";
}

.icon-shopping-bag:before {
  content: "\e70b";
}

.icon-x:before {
  content: "\e70c";
}

.icon-user-check:before {
  content: "\e70d";
}

.icon-watch:before {
  content: "\e70e";
}

.icon-wind:before {
  content: "\e70f";
}

.icon-skip-back:before {
  content: "\e710";
}

.icon-smile:before {
  content: "\e6e7";
}

.icon-users:before {
  content: "\e6e8";
}

.icon-volume-2:before {
  content: "\e6e9";
}

.icon-send:before {
  content: "\e6ea";
}

.icon-zap:before {
  content: "\e6eb";
}

.icon-mail:before {
  content: "\e6ec";
}

.icon-printer:before {
  content: "\e6ed";
}

.icon-maximize:before {
  content: "\e6ee";
}

.icon-zoom-out:before {
  content: "\e6ef";
}

.icon-maximize-2:before {
  content: "\e6f0";
}

.icon-stop-circle:before {
  content: "\e6f1";
}

.icon-wifi:before {
  content: "\e6f2";
}

.icon-framer:before {
  content: "\e6f3";
}

.icon-package:before {
  content: "\e6f4";
}

.icon-share-2:before {
  content: "\e6f5";
}

.icon-tool:before {
  content: "\e6f6";
}

.icon-thermometer:before {
  content: "\e6f7";
}

.icon-italic:before {
  content: "\e6f8";
}

.icon-phone:before {
  content: "\e6f9";
}

.icon-pause-circle:before {
  content: "\e6fa";
}

.icon-layout:before {
  content: "\e6fb";
}

.icon-trash:before {
  content: "\e6d8";
}

.icon-volume:before {
  content: "\e6d9";
}

.icon-slack:before {
  content: "\e6da";
}

.icon-refresh-cw:before {
  content: "\e6db";
}

.icon-pie-chart:before {
  content: "\e6dc";
}

.icon-settings:before {
  content: "\e6dd";
}

.icon-message-square:before {
  content: "\e6de";
}

.icon-play:before {
  content: "\e6df";
}

.icon-rewind:before {
  content: "\e6e0";
}

.icon-plus-square:before {
  content: "\e6e1";
}

.icon-music:before {
  content: "\e6bb";
}

.icon-video:before {
  content: "\e6e2";
}

.icon-youtube:before {
  content: "\e6e3";
}

.icon-phone-off:before {
  content: "\e6bc";
}

.icon-voicemail:before {
  content: "\e6e4";
}

.icon-x-circle:before {
  content: "\e6e5";
}

.icon-x-octagon:before {
  content: "\e6e6";
}

.icon-pause:before {
  content: "\e6bd";
}

.icon-unlock:before {
  content: "\e6c7";
}

.icon-thumbs-down:before {
  content: "\e6c3";
}

.icon-rss:before {
  content: "\e6c1";
}

.icon-shuffle:before {
  content: "\e6bf";
}

.icon-user-minus:before {
  content: "\e6c8";
}

.icon-wifi-off:before {
  content: "\e6c9";
}

.icon-truck:before {
  content: "\e6ca";
}

.icon-trash-2:before {
  content: "\e6cb";
}

.icon-trending-up:before {
  content: "\e6c0";
}

.icon-mouse-pointer:before {
  content: "\e6cc";
}

.icon-upload:before {
  content: "\e6cd";
}

.icon-target:before {
  content: "\e6ce";
}

.icon-x-square:before {
  content: "\e6cf";
}

.icon-user-x:before {
  content: "\e6d0";
}

.icon-sunrise:before {
  content: "\e6be";
}

.icon-tablet:before {
  content: "\e6d1";
}

.icon-user:before {
  content: "\e6d2";
}

.icon-twitch:before {
  content: "\e6d3";
}

.icon-volume-1:before {
  content: "\e6d4";
}

.icon-scissors:before {
  content: "\e6d5";
}

.icon-share:before {
  content: "\e6d6";
}

.icon-smartphone:before {
  content: "\e6d7";
}

.icon-zap-off:before {
  content: "\e6c4";
}

.icon-video-off:before {
  content: "\e6c5";
}

.icon-twitter:before {
  content: "\e6c6";
}

.icon-volume-x:before {
  content: "\e6c2";
}

