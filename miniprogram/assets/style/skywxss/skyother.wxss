
.pageview{
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.minisize{
  width: 18rpx;height: 18rpx;
}
.tinysize{
  width: 24rpx;height: 24rpx;
}
.smallsize{
  width: 32rpx;height: 32rpx;
}
.normalsize{
  width: 48rpx;height: 48rpx;
}
.bigsize{
  width: 64rpx;height: 64rpx;
}
.supersize{
  width: 82rpx;height: 82rpx;
}

.smallradius{
  border-radius:8rpx;
}

.normalradius{
  border-radius: 12rpx;
}
.bigradius{
  border-radius: 24rpx;
}

.superradius{
  border-radius: 36rpx;
  
}
.circleradius{
  border-radius: 50%;
}

.shadowbottom{
  box-shadow: 0rpx 4rpx 5rpx -1rpx #1018281a;
}
.shadowspecial{
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
}

.shadowcenter1{
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1)
}

.shadowcenter2{
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1)

}

.shadowcenter3{
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1)
}


.mla{
  margin-left: auto;
}

.mt8{
  margin-top: 8rpx;
}
.mb8{
  margin-bottom: 8rpx;
}
.mtb8{
  margin-top: 8rpx;margin-bottom: 8rpx;
}
.ml8{
  margin-left: 8rpx;
}
.mr8{
  margin-right: 8rpx;
}
.mlr8{
  margin-left: 8rpx;margin-right: 8rpx;
}

.mt16{
  margin-top: 16rpx;
}
.mb16{
  margin-bottom: 16rpx;
}
.mtb16{
  margin-top: 16rpx;margin-bottom: 16rpx;
}

.ml16{
  margin-left: 16rpx;
}
.mr16{
  margin-right: 16rpx;
}
.mlr16{
  margin-left: 16rpx;margin-right: 16rpx;
}


.mt24{
  margin-top: 24rpx;
}
.mb24{
  margin-bottom: 24rpx;
}
.mtb24{
  margin-top: 24rpx;margin-bottom: 24rpx;
}

.ml24{
  margin-left: 24rpx;
}
.mr24{
  margin-right: 24rpx;
}
.mlr24{
  margin-left: 24rpx;margin-right: 24rpx;
}

.mt32{
  margin-top: 32rpx;
}
.mb32{
  margin-bottom: 32rpx;
}
.mtb32{
  margin-top: 32rpx;margin-bottom: 32rpx;
}

.ml32{
  margin-left: 32rpx;
}
.mr32{
  margin-right: 32rpx;
}
.mlr32{
  margin-left: 32rpx;margin-right: 32rpx;
}

.ml36{
  margin-left: 36rpx;
}
.mr36{
  margin-right: 36rpx;
}
.mlr36{
  margin-left: 36rpx;margin-right: 36rpx;
}

.mt36{
  margin-top: 36rpx;
}
.mb36{
  margin-bottom: 36rpx;
}
.mtb36{
  margin-top: 36rpx;margin-bottom: 36rpx;
}
.ml48{
  margin-left: 48rpx;
}
.mr48{
  margin-right: 48rpx;
}

.mlr48{
  margin-left: 48rpx;margin-right: 48rpx;
}

.mt48{
  margin-top: 48rpx;
}
.mb48{
  margin-bottom: 48rpx;
}
.mtb48{
  margin-top: 48rpx;margin-bottom: 48rpx;
}
.ml64{
  margin-left: 64rpx;
}
.mr64{
  margin-right: 64rpx;
}
.mlr64{
  margin-left: 64rpx;margin-right: 64rpx;
}

.dr{
  display: flex;flex-direction: row;
}
.dc{
  display: flex;flex-direction: column;
}
.acenter{
  align-items: center;
}
.astart{
  align-items: flex-start;
}
.aend{
  align-items: flex-end;

}
.tcenter{
  text-align: center;
}
.jcenter{
  justify-content: center;
}
.jstart{
  justify-content: flex-start;
}
.jend{
  justify-content: flex-end;
}
.jaround{
  justify-content: space-around;
}
.jbetween{
  justify-content: space-between;
}
